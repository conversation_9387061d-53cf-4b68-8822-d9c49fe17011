import React from 'react';
import { useWizard } from '@/hooks/useWizard';
import { TenantTypeSelection } from './wizard/TenantTypeSelection';
import { WizardLayout } from './wizard/WizardLayout';
import { AdminProfile } from './wizard/steps/AdminProfile';
import { PropertyBasic } from './wizard/steps/PropertyBasic';
import { BuildingsFloors } from './wizard/steps/BuildingsFloors';
import { FloorsUnits } from './wizard/steps/FloorsUnits';
import { UnitTypes } from './wizard/steps/UnitTypes';
import { NamingPatterns } from './wizard/steps/NamingPatterns';
import { ParkingAmenities } from './wizard/steps/ParkingAmenities';
import { Amenities } from './wizard/steps/Amenities';
import { MemberAllotment } from './wizard/steps/MemberAllotment';
import { OpeningBalances } from './wizard/steps/OpeningBalances';
import { BillingRules } from './wizard/steps/BillingRules';
import { TestBill } from './wizard/steps/TestBill';
import { Review } from './wizard/steps/Review';
import { TenantType } from '@/types/wizard';
import { useToast } from '@/hooks/use-toast';

export const SetupWizard: React.FC = () => {
  const {
    wizardFlow,
    wizardData,
    initializeWizard,
    nextStep,
    previousStep,
    updateWizardData,
    getProgressPercentage,
    canProceed,
  } = useWizard();

  const { toast } = useToast();

  const handleTenantTypeSelect = (type: TenantType) => {
    initializeWizard(type);
  };

  const handleStepUpdate = (stepData: any) => {
    const currentStep = wizardFlow?.steps[wizardFlow.currentStep];
    
    switch (currentStep?.id) {
      case 'admin-profile':
        updateWizardData({ adminProfile: stepData });
        break;
      case 'property-basic':
      case 'buildings-floors':
      case 'floors-units':
      case 'unit-types':
      case 'naming-patterns':
      case 'parking-amenities':
      case 'amenities':
        updateWizardData({ 
          propertyConfig: { 
            ...wizardData.propertyConfig, 
            ...stepData 
          } 
        });
        break;
    }
  };

  const handleComplete = () => {
    toast({
      title: "Setup Complete!",
      description: "Your property management system has been successfully configured.",
    });
    
    // Here you would typically save the data to your backend
    console.log('Final wizard data:', wizardData);
  };

  const renderStepContent = () => {
    if (!wizardFlow) return null;

    const currentStep = wizardFlow.steps[wizardFlow.currentStep];
    
    switch (currentStep.component) {
      case 'AdminProfile':
        return (
          <AdminProfile
            data={wizardData.adminProfile}
            onUpdate={handleStepUpdate}
          />
        );
      case 'PropertyBasic':
        return (
          <PropertyBasic
            data={wizardData.propertyConfig}
            onUpdate={handleStepUpdate}
          />
        );
      case 'BuildingsFloors':
        return (
          <BuildingsFloors
            data={wizardData.propertyConfig}
            onUpdate={handleStepUpdate}
          />
        );
      case 'FloorsUnits':
        return (
          <FloorsUnits
            data={wizardData.propertyConfig}
            onUpdate={handleStepUpdate}
          />
        );
      case 'UnitTypes':
        return (
          <UnitTypes
            data={wizardData.propertyConfig}
            onUpdate={handleStepUpdate}
            tenantType={wizardData.tenantType}
          />
        );
      case 'NamingPatterns':
        return (
          <NamingPatterns
            data={wizardData.propertyConfig}
            onUpdate={handleStepUpdate}
          />
        );
      case 'ParkingAmenities':
        return (
          <ParkingAmenities
            data={wizardData.propertyConfig}
            onUpdate={handleStepUpdate}
            tenantType={wizardData.tenantType}
          />
        );
      case 'Amenities':
        return (
          <Amenities
            data={wizardData.propertyConfig}
            onUpdate={handleStepUpdate}
          />
        );
      case 'MemberAllotment':
        return (
          <MemberAllotment
            data={wizardData.memberAllotments}
            onUpdate={(data) => updateWizardData({ memberAllotments: data })}
          />
        );
      case 'OpeningBalances':
        return (
          <OpeningBalances
            data={wizardData.openingBalances}
            onUpdate={(data) => updateWizardData({ openingBalances: data })}
          />
        );
      case 'BillingRules':
        return (
          <BillingRules
            data={wizardData.billingRules}
            onUpdate={(data) => updateWizardData({ billingRules: data })}
          />
        );
      case 'TestBill':
        return (
          <TestBill
            data={wizardData.testBill}
            onUpdate={(data) => updateWizardData({ testBill: data })}
            billingRules={wizardData.billingRules}
          />
        );
      case 'Review':
        return (
          <Review
            data={wizardData}
            onComplete={handleComplete}
          />
        );
      default:
        return <div>Step not found</div>;
    }
  };

  // Show tenant type selection if no wizard flow is initialized
  if (!wizardFlow) {
    return <TenantTypeSelection onSelect={handleTenantTypeSelect} />;
  }

  return (
    <WizardLayout
      wizardFlow={wizardFlow}
      progressPercentage={getProgressPercentage()}
      onPrevious={previousStep}
      onNext={nextStep}
      canProceed={canProceed()}
    >
      {renderStepContent()}
    </WizardLayout>
  );
};