import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, Store, Users } from 'lucide-react';
import { TenantType } from '@/types/wizard';

interface TenantTypeSelectionProps {
  onSelect: (type: TenantType) => void;
}

const tenantTypes = [
  {
    type: 'individual' as TenantType,
    title: 'Individual House',
    description: 'Single property owner managing their own house',
    icon: Building2,
    features: ['Single property', 'Basic amenities', 'Simple billing'],
  },
  {
    type: 'commercial' as TenantType,
    title: 'Commercial Establishment',
    description: 'Business premises with multiple tenants',
    icon: Store,
    features: ['Multiple tenants', 'Commercial units', 'Tenant management'],
  },
  {
    type: 'society' as TenantType,
    title: 'Housing Society',
    description: 'Residential society with multiple members',
    icon: Users,
    features: ['Multiple buildings', 'Member management', 'Advanced amenities'],
  },
];

export const TenantTypeSelection: React.FC<TenantTypeSelectionProps> = ({ onSelect }) => {
  return (
    <div className="min-h-screen bg-wizard-gradient flex items-center justify-center">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Welcome to Property Management Setup
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Let's start by understanding what type of property you're managing. 
            This will help us customize the setup process for your specific needs.
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <h2 className="text-2xl font-semibold text-white text-center mb-8">
            Choose Your Property Type
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {tenantTypes.map((tenant) => {
              const IconComponent = tenant.icon;
              return (
                <Card
                  key={tenant.type}
                  className="p-8 hover:shadow-wizard transition-all duration-300 hover:scale-105 cursor-pointer group"
                  onClick={() => onSelect(tenant.type)}
                >
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6 group-hover:bg-primary/20 transition-colors">
                      <IconComponent className="w-8 h-8 text-primary" />
                    </div>
                    
                    <h3 className="text-xl font-bold text-card-foreground mb-3">
                      {tenant.title}
                    </h3>
                    
                    <p className="text-muted-foreground mb-6">
                      {tenant.description}
                    </p>
                    
                    <ul className="space-y-2 mb-8">
                      {tenant.features.map((feature, index) => (
                        <li key={index} className="text-sm text-muted-foreground flex items-center justify-center">
                          <span className="w-2 h-2 bg-accent rounded-full mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    
                    <Button className="w-full" size="lg">
                      Select This Type
                    </Button>
                  </div>
                </Card>
              );
            })}
          </div>
          
          <div className="text-center mt-12">
            <p className="text-white/60 text-sm">
              Don't worry, you can always modify these settings later
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};