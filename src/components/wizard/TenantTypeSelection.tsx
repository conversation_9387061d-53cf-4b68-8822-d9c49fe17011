import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, Store, Users } from 'lucide-react';
import { TenantType } from '@/types/wizard';

interface TenantTypeSelectionProps {
  onSelect: (type: TenantType) => void;
}

const tenantTypes = [
  {
    type: 'individual' as TenantType,
    title: 'Individual House',
    description: 'Single property owner managing their own house',
    icon: Building2,
    features: ['Single property', 'Basic amenities', 'Simple billing'],
  },
  {
    type: 'commercial' as TenantType,
    title: 'Commercial Establishment',
    description: 'Business premises with multiple tenants',
    icon: Store,
    features: ['Multiple tenants', 'Commercial units', 'Tenant management'],
  },
  {
    type: 'society' as TenantType,
    title: 'Housing Society',
    description: 'Residential society with multiple members',
    icon: Users,
    features: ['Multiple buildings', 'Member management', 'Advanced amenities'],
  },
];

export const TenantTypeSelection: React.FC<TenantTypeSelectionProps> = ({ onSelect }) => {
  return (
    <div className="min-h-screen bg-wizard-gradient relative overflow-hidden flex items-center justify-center py-4">
      {/* Background decoration - reduced opacity for better text contrast */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-primary/5 pointer-events-none" />
      <div className="absolute top-0 right-0 w-80 h-80 bg-primary/3 rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2 pointer-events-none" />
      <div className="absolute bottom-0 left-0 w-80 h-80 bg-primary/3 rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2 pointer-events-none" />

      <div className="container mx-auto px-4 relative z-10 max-w-7xl">
        <div className="text-center mb-8">
          {/* One Society Logo - Centered above heading */}
          <div className="flex justify-center mb-6">
            <img
              src="/One Society.png"
              alt="One Society"
              className="h-16 w-auto object-contain"
            />
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 tracking-tight">
            Welcome to <span className="text-primary">One Society</span>
          </h1>
          <p className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto leading-relaxed">
            Let's start by understanding what type of property you're managing.
            This will help us customize the setup process for your specific needs.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="glass rounded-2xl p-6 mb-6">
            <h2 className="text-2xl font-bold text-center mb-8 text-foreground">
              Choose Your Property Type
            </h2>

            <div className="grid md:grid-cols-3 gap-6">
              {tenantTypes.map((tenant) => {
                const IconComponent = tenant.icon;
                return (
                  <div
                    key={tenant.type}
                    className="group cursor-pointer"
                    onClick={() => onSelect(tenant.type)}
                  >
                    <div className="card-enhanced p-6 h-full hover:shadow-lg transition-all duration-300 hover:scale-102 hover:-translate-y-1 bg-card/95 backdrop-blur-sm border hover:border-primary/30">
                      <div className="text-center h-full flex flex-col">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mb-6 group-hover:bg-primary/20 group-hover:scale-105 transition-all duration-300 mx-auto">
                          <IconComponent className="w-8 h-8 text-primary group-hover:scale-105 transition-transform duration-300" />
                        </div>

                        <h3 className="text-xl font-bold text-card-foreground mb-3 group-hover:text-primary transition-colors duration-300">
                          {tenant.title}
                        </h3>

                        <p className="text-muted-foreground mb-6 text-base leading-relaxed flex-grow">
                          {tenant.description}
                        </p>

                        <ul className="space-y-2 mb-6">
                          {tenant.features.map((feature, index) => (
                            <li key={index} className="text-sm text-muted-foreground flex items-center justify-center group-hover:text-foreground transition-colors duration-300">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mr-2 group-hover:scale-110 transition-transform duration-300" />
                              {feature}
                            </li>
                          ))}
                        </ul>

                        <Button
                          variant="premium"
                          size="default"
                          className="w-full group-hover:scale-102 transition-transform duration-300"
                        >
                          Select This Type
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="text-center">
            <p className="text-gray-600 text-base">
              Don't worry, you can always modify these settings later
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};