import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, Store, Users } from 'lucide-react';
import { TenantType } from '@/types/wizard';

interface TenantTypeSelectionProps {
  onSelect: (type: TenantType) => void;
}

const tenantTypes = [
  {
    type: 'individual' as TenantType,
    title: 'Individual House',
    description: 'Single property owner managing their own house',
    icon: Building2,
    features: ['Single property', 'Basic amenities', 'Simple billing'],
  },
  {
    type: 'commercial' as TenantType,
    title: 'Commercial Establishment',
    description: 'Business premises with multiple tenants',
    icon: Store,
    features: ['Multiple tenants', 'Commercial units', 'Tenant management'],
  },
  {
    type: 'society' as TenantType,
    title: 'Housing Society',
    description: 'Residential society with multiple members',
    icon: Users,
    features: ['Multiple buildings', 'Member management', 'Advanced amenities'],
  },
];

export const TenantTypeSelection: React.FC<TenantTypeSelectionProps> = ({ onSelect }) => {
  return (
    <div className="min-h-screen bg-wizard-gradient relative overflow-hidden flex items-center justify-center">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 pointer-events-none" />
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2 pointer-events-none" />
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2 pointer-events-none" />

      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="text-center mb-16">
          {/* Logo */}
          <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-3xl mb-8">
            <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center">
              <span className="text-primary font-bold text-2xl">OS</span>
            </div>
          </div>

          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 tracking-tight">
            Welcome to <span className="gradient-text">One Society</span>
          </h1>
          <p className="text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Let's start by understanding what type of property you're managing.
            This will help us customize the setup process for your specific needs.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="glass rounded-3xl p-8 mb-12">
            <h2 className="text-3xl font-bold text-center mb-12 text-foreground">
              Choose Your Property Type
            </h2>

            <div className="grid md:grid-cols-3 gap-8">
              {tenantTypes.map((tenant) => {
                const IconComponent = tenant.icon;
                return (
                  <div
                    key={tenant.type}
                    className="group cursor-pointer"
                    onClick={() => onSelect(tenant.type)}
                  >
                    <div className="card-enhanced p-8 h-full hover:shadow-wizard transition-all duration-500 hover:scale-105 hover:-translate-y-2 bg-card/95 backdrop-blur-sm border-2 hover:border-primary/30">
                      <div className="text-center h-full flex flex-col">
                        <div className="inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-3xl mb-8 group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-500 mx-auto">
                          <IconComponent className="w-10 h-10 text-primary group-hover:scale-110 transition-transform duration-300" />
                        </div>

                        <h3 className="text-2xl font-bold text-card-foreground mb-4 group-hover:text-primary transition-colors duration-300">
                          {tenant.title}
                        </h3>

                        <p className="text-muted-foreground mb-8 text-lg leading-relaxed flex-grow">
                          {tenant.description}
                        </p>

                        <ul className="space-y-3 mb-10">
                          {tenant.features.map((feature, index) => (
                            <li key={index} className="text-sm text-muted-foreground flex items-center justify-center group-hover:text-foreground transition-colors duration-300">
                              <div className="w-2 h-2 bg-primary rounded-full mr-3 group-hover:scale-125 transition-transform duration-300" />
                              {feature}
                            </li>
                          ))}
                        </ul>

                        <Button
                          variant="premium"
                          size="lg"
                          className="w-full group-hover:scale-105 transition-transform duration-300"
                        >
                          Select This Type
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="text-center">
            <p className="text-white/60 text-lg">
              Don't worry, you can always modify these settings later
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};