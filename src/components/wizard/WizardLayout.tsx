import React from 'react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { WizardFlow } from '@/types/wizard';

interface WizardLayoutProps {
  wizardFlow: WizardFlow;
  progressPercentage: number;
  onPrevious: () => void;
  onNext: () => void;
  canProceed: boolean;
  children: React.ReactNode;
}

export const WizardLayout: React.FC<WizardLayoutProps> = ({
  wizardFlow,
  progressPercentage,
  onPrevious,
  onNext,
  canProceed,
  children,
}) => {
  const currentStep = wizardFlow.steps[wizardFlow.currentStep];
  const isFirstStep = wizardFlow.currentStep === 0;
  const isLastStep = wizardFlow.currentStep === wizardFlow.steps.length - 1;

  return (
    <div className="min-h-screen bg-wizard-gradient">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Property Management Setup
          </h1>
          <p className="text-white/80">
            Let's configure your property management system step by step
          </p>
        </div>

        {/* Progress Bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium text-white/90">
              Step {wizardFlow.currentStep + 1} of {wizardFlow.steps.length}
            </span>
            <span className="text-lg font-bold text-white bg-white/20 px-3 py-1 rounded-full">
              {progressPercentage}%
            </span>
          </div>
          <div className="relative">
            <Progress 
              value={progressPercentage} 
              className="h-4 bg-white/20"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-semibold text-white drop-shadow-lg">
                {progressPercentage}% Complete
              </span>
            </div>
          </div>
        </div>

        {/* Step Navigation */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex items-center justify-center space-x-4 overflow-x-auto pb-4">
            {wizardFlow.steps.map((step, index) => (
              <div
                key={step.id}
                className="flex items-center flex-shrink-0"
              >
                <div
                  className={`
                    flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-all duration-300
                    ${index === wizardFlow.currentStep
                      ? 'bg-white text-primary shadow-lg'
                      : wizardFlow.completedSteps.includes(index)
                      ? 'bg-success text-white'
                      : 'bg-white/20 text-white/60'
                    }
                  `}
                >
                  {index + 1}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    index === wizardFlow.currentStep ? 'text-white' : 'text-white/70'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < wizardFlow.steps.length - 1 && (
                  <div className="ml-4 w-8 h-0.5 bg-white/20" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          <Card className="p-8 shadow-card">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-card-foreground mb-2">
                {currentStep.title}
              </h2>
              {currentStep.description && (
                <p className="text-muted-foreground">
                  {currentStep.description}
                </p>
              )}
            </div>

            <div className="mb-8">
              {children}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={onPrevious}
                disabled={isFirstStep}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Previous</span>
              </Button>

              <div className="text-sm text-muted-foreground">
                {currentStep.isRequired && (
                  <span className="text-destructive">* Required</span>
                )}
              </div>

              <Button
                onClick={onNext}
                disabled={!canProceed}
                className="flex items-center space-x-2"
              >
                <span>{isLastStep ? 'Complete Setup' : 'Next'}</span>
                <ArrowRight className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};