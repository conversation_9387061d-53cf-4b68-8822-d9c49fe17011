import React from 'react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { WizardFlow } from '@/types/wizard';

interface WizardLayoutProps {
  wizardFlow: WizardFlow;
  progressPercentage: number;
  onPrevious: () => void;
  onNext: () => void;
  canProceed: boolean;
  children: React.ReactNode;
}

export const WizardLayout: React.FC<WizardLayoutProps> = ({
  wizardFlow,
  progressPercentage,
  onPrevious,
  onNext,
  canProceed,
  children,
}) => {
  const currentStep = wizardFlow.steps[wizardFlow.currentStep];
  const isFirstStep = wizardFlow.currentStep === 0;
  const isLastStep = wizardFlow.currentStep === wizardFlow.steps.length - 1;

  return (
    <div className="min-h-screen bg-wizard-gradient relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 pointer-events-none" />
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2 pointer-events-none" />
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2 pointer-events-none" />

      <div className="container mx-auto px-4 py-6 relative z-10">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-14 h-14 bg-white/10 backdrop-blur-sm rounded-xl mb-4">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
              <span className="text-primary font-bold text-lg">OS</span>
            </div>
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-3 tracking-tight">
            Property Management Setup
          </h1>
          <p className="text-lg text-white/80 max-w-xl mx-auto leading-relaxed">
            Let's configure your property management system step by step
          </p>
        </div>

        {/* Enhanced Progress Section */}
        <div className="max-w-3xl mx-auto mb-8">
          <div className="glass rounded-xl p-5 mb-6">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-primary rounded-full animate-pulse" />
                <span className="text-sm font-medium text-foreground">
                  Step {wizardFlow.currentStep + 1} of {wizardFlow.steps.length}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold text-primary">
                  {progressPercentage}%
                </span>
                <span className="text-sm text-muted-foreground">Complete</span>
              </div>
            </div>

            <div className="relative">
              <div className="h-3 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-primary to-primary-glow rounded-full transition-all duration-700 ease-out relative"
                  style={{ width: `${progressPercentage}%` }}
                >
                  <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Step Navigation */}
        <div className="max-w-5xl mx-auto mb-8">
          <div className="glass rounded-xl p-5">
            <div className="flex items-center justify-center space-x-2 md:space-x-4 overflow-x-auto pb-2">
              {wizardFlow.steps.map((step, index) => (
                <div
                  key={step.id}
                  className="flex items-center flex-shrink-0 group"
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`
                        flex items-center justify-center w-12 h-12 rounded-2xl text-sm font-semibold transition-all duration-500 relative
                        ${index === wizardFlow.currentStep
                          ? 'bg-primary text-white shadow-button scale-110'
                          : wizardFlow.completedSteps.includes(index)
                          ? 'bg-success text-white shadow-lg'
                          : 'bg-muted text-muted-foreground hover:bg-muted/80'
                        }
                      `}
                    >
                      {wizardFlow.completedSteps.includes(index) ? (
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        index + 1
                      )}
                      {index === wizardFlow.currentStep && (
                        <div className="absolute inset-0 rounded-2xl bg-primary/20 animate-pulse" />
                      )}
                    </div>
                    <div className="mt-3 text-center max-w-24">
                      <p className={`text-xs font-medium leading-tight ${
                        index === wizardFlow.currentStep
                          ? 'text-primary'
                          : wizardFlow.completedSteps.includes(index)
                          ? 'text-success'
                          : 'text-muted-foreground'
                      }`}>
                        {step.title}
                      </p>
                    </div>
                  </div>
                  {index < wizardFlow.steps.length - 1 && (
                    <div className={`mx-2 md:mx-4 w-8 md:w-12 h-0.5 rounded-full transition-colors duration-500 ${
                      wizardFlow.completedSteps.includes(index)
                        ? 'bg-success'
                        : 'bg-border'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-5xl mx-auto">
          <div className="card-enhanced p-6 md:p-8 bg-card/95 backdrop-blur-sm border-0 shadow-card-hover">
            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-2 h-8 bg-primary rounded-full" />
                <div>
                  <h2 className="text-3xl font-bold text-card-foreground mb-2 tracking-tight">
                    {currentStep.title}
                  </h2>
                  {currentStep.description && (
                    <p className="text-lg text-muted-foreground leading-relaxed">
                      {currentStep.description}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="mb-8">
              {children}
            </div>

            {/* Enhanced Navigation */}
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-border">
              <Button
                variant="outline"
                onClick={onPrevious}
                disabled={isFirstStep}
                className="flex items-center space-x-3 h-12 px-6 rounded-xl border-2 hover:border-primary/50 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed order-2 sm:order-1"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="font-medium">Previous</span>
              </Button>

              <div className="flex items-center space-x-4 order-1 sm:order-2">
                {currentStep.isRequired && (
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-primary rounded-full" />
                    <span className="text-muted-foreground">Required step</span>
                  </div>
                )}
                <div className="text-sm text-muted-foreground">
                  {wizardFlow.currentStep + 1} of {wizardFlow.steps.length}
                </div>
              </div>

              <Button
                onClick={onNext}
                disabled={!canProceed}
                className="btn-primary flex items-center space-x-3 h-12 px-8 rounded-xl font-semibold text-base disabled:opacity-50 disabled:cursor-not-allowed order-3"
              >
                <span>{isLastStep ? 'Complete Setup' : 'Continue'}</span>
                <ArrowRight className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};