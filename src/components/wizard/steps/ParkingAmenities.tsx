import React from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { PropertyConfig, Amenity } from '@/types/wizard';

interface ParkingAmenitiesProps {
  data?: Partial<PropertyConfig>;
  onUpdate: (data: Partial<PropertyConfig>) => void;
  tenantType?: string;
}

const commonAmenities = [
  'Swimming Pool',
  'Gymnasium',
  'Club House',
  'Children\'s Play Area',
  'Garden/Landscaping',
  'Security System',
  'CCTV Surveillance',
  'Intercom Facility',
  'Power Backup',
  'Water Storage',
  'Waste Management',
  'Maintenance Office',
];

const residentialAmenities = [
  'Party Hall',
  'Senior Citizen Area',
  'Jogging Track',
  'Indoor Games Room',
  'Library',
  'Multipurpose Hall',
  'Visitors Parking',
  'Guest Rooms',
];

const commercialAmenities = [
  'Reception/Lobby',
  'Conference Rooms',
  'Cafeteria',
  'Business Center',
  'Server Room',
  'Client Parking',
  'Loading Dock',
  'Storage Area',
];

export const ParkingAmenities: React.FC<ParkingAmenitiesProps> = ({ 
  data, 
  onUpdate, 
  tenantType 
}) => {
  const [parkingSpaces, setParkingSpaces] = React.useState(data?.parkingSpaces || 0);
  const [selectedAmenities, setSelectedAmenities] = React.useState<Amenity[]>(
    (data?.amenities || []).map((name, index) => 
      typeof name === 'string' 
        ? { id: `amenity-${index}`, name, isSelected: true }
        : name
    )
  );
  const [customAmenity, setCustomAmenity] = React.useState('');

  const allAmenities = [
    ...commonAmenities,
    ...(tenantType === 'commercial' ? commercialAmenities : residentialAmenities),
  ];

  React.useEffect(() => {
    onUpdate({
      ...data,
      parkingSpaces,
      amenities: selectedAmenities,
    });
  }, [parkingSpaces, selectedAmenities, data, onUpdate]);

  const toggleAmenity = (amenityName: string) => {
    setSelectedAmenities(prev => {
      const exists = prev.find(a => a.name === amenityName);
      if (exists) {
        return prev.filter(a => a.name !== amenityName);
      } else {
        const newAmenity: Amenity = {
          id: `amenity-${Date.now()}`,
          name: amenityName,
          isSelected: true
        };
        return [...prev, newAmenity];
      }
    });
  };

  const addCustomAmenity = () => {
    if (customAmenity.trim() && !selectedAmenities.find(a => a.name === customAmenity.trim())) {
      const newAmenity: Amenity = {
        id: `custom-${Date.now()}`,
        name: customAmenity.trim(),
        isSelected: true
      };
      setSelectedAmenities(prev => [...prev, newAmenity]);
      setCustomAmenity('');
    }
  };

  const removeCustomAmenity = (amenityName: string) => {
    if (!allAmenities.includes(amenityName)) {
      setSelectedAmenities(prev => prev.filter(a => a.name !== amenityName));
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Parking Configuration</h3>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="parkingSpaces">Total Parking Spaces</Label>
            <Input
              id="parkingSpaces"
              type="number"
              min="0"
              value={parkingSpaces}
              onChange={(e) => setParkingSpaces(parseInt(e.target.value) || 0)}
              placeholder="e.g., 50"
              className="max-w-xs"
            />
            <p className="text-sm text-muted-foreground">
              Include all types: covered, open, visitor parking
            </p>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h3 className="font-semibold mb-4">Amenities & Facilities</h3>
        
        <div className="space-y-6">
          <div>
            <Label className="text-base mb-3 block">Common Amenities:</Label>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
              {commonAmenities.map((amenity) => (
                <div key={amenity} className="flex items-center space-x-2">
                  <Checkbox
                    id={amenity}
                    checked={selectedAmenities.some(a => a.name === amenity)}
                    onCheckedChange={() => toggleAmenity(amenity)}
                  />
                  <Label htmlFor={amenity} className="text-sm cursor-pointer">
                    {amenity}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <Label className="text-base mb-3 block">
              {tenantType === 'commercial' ? 'Commercial' : 'Residential'} Specific:
            </Label>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
              {(tenantType === 'commercial' ? commercialAmenities : residentialAmenities).map((amenity) => (
                <div key={amenity} className="flex items-center space-x-2">
                  <Checkbox
                    id={amenity}
                    checked={selectedAmenities.some(a => a.name === amenity)}
                    onCheckedChange={() => toggleAmenity(amenity)}
                  />
                  <Label htmlFor={amenity} className="text-sm cursor-pointer">
                    {amenity}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="border-t pt-4">
            <Label className="text-base mb-3 block">Add Custom Amenity:</Label>
            <div className="flex space-x-2">
              <Input
                value={customAmenity}
                onChange={(e) => setCustomAmenity(e.target.value)}
                placeholder="Enter custom amenity"
                className="flex-1"
                onKeyPress={(e) => e.key === 'Enter' && addCustomAmenity()}
              />
              <Button onClick={addCustomAmenity} disabled={!customAmenity.trim()}>
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {(parkingSpaces > 0 || selectedAmenities.length > 0) && (
        <Card className="p-6 bg-success/5">
          <h3 className="font-semibold mb-4">Summary</h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Parking</h4>
              <p className="text-sm text-muted-foreground">
                Total spaces: <span className="font-medium">{parkingSpaces}</span>
              </p>
            </div>
            
            {selectedAmenities.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Amenities ({selectedAmenities.length})</h4>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {selectedAmenities.map((amenity) => (
                    <div key={amenity.id} className="flex items-center justify-between text-sm">
                      <span>{amenity.name}</span>
                      {!allAmenities.includes(amenity.name) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCustomAmenity(amenity.name)}
                          className="text-destructive hover:text-destructive p-1 h-auto"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};