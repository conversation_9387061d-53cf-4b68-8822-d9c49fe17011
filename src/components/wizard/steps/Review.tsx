import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Building2, Users, Settings, MapPin } from 'lucide-react';
import { WizardData } from '@/types/wizard';

interface ReviewProps {
  data: WizardData;
  onComplete: () => void;
}

export const Review: React.FC<ReviewProps> = ({ data, onComplete }) => {
  const { tenantType, adminProfile, propertyConfig } = data;

  const getTenantTypeLabel = () => {
    switch (tenantType) {
      case 'individual': return 'Individual House';
      case 'commercial': return 'Commercial Establishment';
      case 'society': return 'Housing Society';
      default: return 'Unknown';
    }
  };

  const getTotalUnits = () => {
    if (!propertyConfig) return 0;
    
    if (typeof propertyConfig.unitsPerFloor === 'number') {
      const totalFloors = Array.isArray(propertyConfig.floorsPerBuilding)
        ? propertyConfig.floorsPerBuilding.reduce((sum, floors) => sum + floors, 0)
        : (propertyConfig.floorsPerBuilding || 0) * (propertyConfig.numberOfBuildings || 0);
      return totalFloors * propertyConfig.unitsPerFloor;
    }
    
    if (Array.isArray(propertyConfig.unitsPerFloor)) {
      return propertyConfig.unitsPerFloor.reduce((buildingSum, building) => 
        buildingSum + (Array.isArray(building) ? building.reduce((floorSum, units) => floorSum + units, 0) : 0), 0
      );
    }
    
    return 0;
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <CheckCircle className="w-16 h-16 text-success mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Setup Review</h2>
        <p className="text-muted-foreground">
          Please review your configuration before completing the setup
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Admin Profile */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <Users className="w-5 h-5 text-primary mr-2" />
            <h3 className="font-semibold">Admin Profile</h3>
          </div>
          <div className="space-y-2 text-sm">
            <p><strong>Name:</strong> {adminProfile?.firstName} {adminProfile?.lastName}</p>
            <p><strong>Email:</strong> {adminProfile?.email}</p>
            {adminProfile?.phone && <p><strong>Phone:</strong> {adminProfile.phone}</p>}
            {adminProfile?.companyName && <p><strong>Company:</strong> {adminProfile.companyName}</p>}
          </div>
        </Card>

        {/* Property Type */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <Building2 className="w-5 h-5 text-primary mr-2" />
            <h3 className="font-semibold">Property Type</h3>
          </div>
          <div className="space-y-2">
            <Badge variant="secondary" className="mb-2">
              {getTenantTypeLabel()}
            </Badge>
            <p className="text-sm">
              <strong>Property:</strong> {propertyConfig?.propertyType}
            </p>
          </div>
        </Card>
      </div>

      {/* Property Configuration */}
      <Card className="p-6">
        <div className="flex items-center mb-4">
          <Settings className="w-5 h-5 text-primary mr-2" />
          <h3 className="font-semibold">Property Configuration</h3>
        </div>
        
        <div className="grid md:grid-cols-3 gap-6">
          <div className="space-y-2 text-sm">
            <h4 className="font-medium">Structure</h4>
            <p>Buildings: {propertyConfig?.numberOfBuildings || 0}</p>
            <p>Gates: {propertyConfig?.numberOfGates || 0}</p>
            {tenantType !== 'individual' && (
              <>
                <p>Total Floors: {
                  Array.isArray(propertyConfig?.floorsPerBuilding)
                    ? propertyConfig.floorsPerBuilding.reduce((sum, floors) => sum + floors, 0)
                    : (propertyConfig?.floorsPerBuilding || 0) * (propertyConfig?.numberOfBuildings || 0)
                }</p>
                <p>Total Units: {getTotalUnits()}</p>
              </>
            )}
          </div>
          
          {propertyConfig?.unitTypes && propertyConfig.unitTypes.length > 0 && (
            <div className="space-y-2 text-sm">
              <h4 className="font-medium">Unit Types ({propertyConfig.unitTypes.length})</h4>
              <div className="flex flex-wrap gap-1">
                {propertyConfig.unitTypes.slice(0, 3).map((type) => (
                  <Badge key={type.id} variant="outline" className="text-xs">
                    {type.name}
                  </Badge>
                ))}
                {propertyConfig.unitTypes.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{propertyConfig.unitTypes.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}
          
          <div className="space-y-2 text-sm">
            <h4 className="font-medium">Facilities</h4>
            <p>Parking: {propertyConfig?.parkingSpaces || 0} spaces</p>
            <p>Amenities: {propertyConfig?.amenities?.length || 0} items</p>
          </div>
        </div>
      </Card>

      {/* Address */}
      {adminProfile?.address && (
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <MapPin className="w-5 h-5 text-primary mr-2" />
            <h3 className="font-semibold">Property Address</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            {adminProfile.address}
            {adminProfile.city && `, ${adminProfile.city}`}
            {adminProfile.state && `, ${adminProfile.state}`}
            {adminProfile.pincode && ` - ${adminProfile.pincode}`}
          </p>
        </Card>
      )}

      <Card className="p-6 bg-success/5 border-success/20">
        <div className="text-center">
          <h3 className="font-semibold mb-2 text-success-foreground">Ready to Complete Setup!</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Your property management system is configured and ready to use. 
            Click below to finalize the setup and start managing your property.
          </p>
          
          <Button 
            onClick={onComplete}
            size="lg"
            className="bg-success hover:bg-success/90"
          >
            Complete Setup & Launch System
          </Button>
          
          <p className="text-xs text-muted-foreground mt-4">
            You can modify these settings anytime from the admin dashboard
          </p>
        </div>
      </Card>
    </div>
  );
};