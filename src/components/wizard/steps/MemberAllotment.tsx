import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Users, Plus, Trash2 } from 'lucide-react';
import { Member } from '@/types/wizard';

interface MemberAllotmentProps {
  data?: Member[];
  onUpdate: (data: Member[]) => void;
}

export const MemberAllotment: React.FC<MemberAllotmentProps> = ({ data, onUpdate }) => {
  const [members, setMembers] = React.useState<Member[]>(data || []);
  const [newMember, setNewMember] = React.useState({
    name: '',
    email: '',
    phone: '',
    unitNumber: '',
    type: 'primary' as 'primary' | 'secondary'
  });

  React.useEffect(() => {
    onUpdate(members);
  }, [members, onUpdate]);

  const addMember = () => {
    if (newMember.name.trim() && newMember.unitNumber.trim()) {
      const member: Member = {
        id: Date.now().toString(),
        name: newMember.name.trim(),
        email: newMember.email.trim(),
        phone: newMember.phone.trim(),
        unitNumber: newMember.unitNumber.trim(),
        type: newMember.type
      };
      setMembers(prev => [...prev, member]);
      setNewMember({
        name: '',
        email: '',
        phone: '',
        unitNumber: '',
        type: 'primary'
      });
    }
  };

  const removeMember = (memberId: string) => {
    setMembers(prev => prev.filter(m => m.id !== memberId));
  };

  const groupedByUnit = members.reduce((acc, member) => {
    const unit = member.unitNumber || 'Unassigned';
    if (!acc[unit]) acc[unit] = [];
    acc[unit].push(member);
    return acc;
  }, {} as Record<string, Member[]>);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <Users className="w-12 h-12 text-primary mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          Member Allotment
        </h3>
        <p className="text-muted-foreground">
          Assign primary and secondary members to units. You can skip this step and complete it later.
        </p>
      </div>

      {/* Add New Member Form */}
      <Card className="p-6">
        <h4 className="font-medium mb-4">Add New Member</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="memberName">Name *</Label>
            <Input
              id="memberName"
              value={newMember.name}
              onChange={(e) => setNewMember(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter member name"
            />
          </div>
          <div>
            <Label htmlFor="memberEmail">Email</Label>
            <Input
              id="memberEmail"
              type="email"
              value={newMember.email}
              onChange={(e) => setNewMember(prev => ({ ...prev, email: e.target.value }))}
              placeholder="Enter email address"
            />
          </div>
          <div>
            <Label htmlFor="memberPhone">Phone</Label>
            <Input
              id="memberPhone"
              value={newMember.phone}
              onChange={(e) => setNewMember(prev => ({ ...prev, phone: e.target.value }))}
              placeholder="Enter phone number"
            />
          </div>
          <div>
            <Label htmlFor="unitNumber">Unit Number *</Label>
            <Input
              id="unitNumber"
              value={newMember.unitNumber}
              onChange={(e) => setNewMember(prev => ({ ...prev, unitNumber: e.target.value }))}
              placeholder="e.g., A-101"
            />
          </div>
          <div>
            <Label htmlFor="memberType">Member Type</Label>
            <select
              id="memberType"
              value={newMember.type}
              onChange={(e) => setNewMember(prev => ({ ...prev, type: e.target.value as 'primary' | 'secondary' }))}
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="primary">Primary</option>
              <option value="secondary">Secondary</option>
            </select>
          </div>
          <div className="flex items-end">
            <Button onClick={addMember} className="w-full">
              <Plus className="w-4 h-4 mr-2" />
              Add Member
            </Button>
          </div>
        </div>
      </Card>

      {/* Members List by Unit */}
      <div className="space-y-4">
        <h4 className="font-medium">Members by Unit ({members.length} total)</h4>
        
        {Object.keys(groupedByUnit).length === 0 ? (
          <Card className="p-8 text-center">
            <Users className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No members added yet</p>
            <p className="text-sm text-muted-foreground mt-1">
              Add members using the form above or skip this step to complete later
            </p>
          </Card>
        ) : (
          <div className="grid gap-4">
            {Object.entries(groupedByUnit).map(([unitNumber, unitMembers]) => (
              <Card key={unitNumber} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-medium">Unit {unitNumber}</h5>
                  <Badge variant="outline">
                    {unitMembers.length} member{unitMembers.length !== 1 ? 's' : ''}
                  </Badge>
                </div>
                <div className="space-y-2">
                  {unitMembers.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{member.name}</span>
                          <Badge variant={member.type === 'primary' ? 'default' : 'secondary'} className="text-xs">
                            {member.type}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          {member.email && <span>{member.email}</span>}
                          {member.email && member.phone && <span> • </span>}
                          {member.phone && <span>{member.phone}</span>}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeMember(member.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Card className="p-4 bg-accent/10 border-accent/20">
        <p className="text-sm text-muted-foreground">
          <strong>Note:</strong> You can skip member allotment and complete it later from the main dashboard. 
          This step is optional for the initial setup.
        </p>
      </Card>
    </div>
  );
};