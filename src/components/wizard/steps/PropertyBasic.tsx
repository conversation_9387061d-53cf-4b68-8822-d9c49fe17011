import React from 'react';
import { useForm } from 'react-hook-form';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PropertyConfig } from '@/types/wizard';

interface PropertyBasicProps {
  data?: Partial<PropertyConfig>;
  onUpdate: (data: Partial<PropertyConfig>) => void;
}

const propertyTypes = [
  'Residential Apartment',
  'Commercial Complex',
  'Mixed Use Building',
  'Independent House',
  'Villa Complex',
  'Office Building',
  'Retail Complex',
  'Industrial Complex',
];

export const PropertyBasic: React.FC<PropertyBasicProps> = ({ data, onUpdate }) => {
  const { register, watch, setValue } = useForm<Partial<PropertyConfig>>({
    defaultValues: data || {},
  });

  const formData = watch();

  React.useEffect(() => {
    if (formData.propertyType && formData.numberOfBuildings) {
      onUpdate(formData);
    }
  }, [formData, onUpdate]);

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-primary/5">
        <h3 className="font-semibold mb-4 text-primary">Basic Property Information</h3>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="propertyType">Property Type *</Label>
            <Select 
              value={formData.propertyType} 
              onValueChange={(value) => setValue('propertyType', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select property type" />
              </SelectTrigger>
              <SelectContent>
                {propertyTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="numberOfBuildings">Number of Buildings *</Label>
              <Input
                id="numberOfBuildings"
                type="number"
                min="1"
                {...register('numberOfBuildings', { 
                  required: true,
                  valueAsNumber: true 
                })}
                placeholder="e.g., 1"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="numberOfGates">Number of Gates/Entrances</Label>
              <Input
                id="numberOfGates"
                type="number"
                min="1"
                {...register('numberOfGates', { valueAsNumber: true })}
                placeholder="e.g., 2"
              />
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-accent/5">
        <h3 className="font-semibold mb-4 text-accent-foreground">Property Description</h3>
        <p className="text-muted-foreground text-sm">
          Based on your selections, we'll guide you through configuring:
        </p>
        <ul className="mt-3 space-y-1 text-sm text-muted-foreground">
          <li>• Building structures and floor layouts</li>
          <li>• Unit configurations and types</li>
          <li>• Naming patterns for easy identification</li>
          <li>• Parking and amenity allocations</li>
        </ul>
      </Card>
    </div>
  );
};