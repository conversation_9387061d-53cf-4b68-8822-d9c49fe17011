import React from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { PropertyConfig } from '@/types/wizard';

interface FloorsUnitsProps {
  data?: Partial<PropertyConfig>;
  onUpdate: (data: Partial<PropertyConfig>) => void;
}

export const FloorsUnits: React.FC<FloorsUnitsProps> = ({ data, onUpdate }) => {
  const [equalUnits, setEqualUnits] = React.useState(data?.equalUnits ?? true);
  const [unitsPerFloor, setUnitsPerFloor] = React.useState<number>(4);
  const [customUnits, setCustomUnits] = React.useState<number[][]>([]);

  // Calculate total buildings and floors for display
  const numberOfBuildings = data?.numberOfBuildings || 1;
  const floorsPerBuilding = Array.isArray(data?.floorsPerBuilding) 
    ? data.floorsPerBuilding 
    : Array(numberOfBuildings).fill(data?.floorsPerBuilding || 1);

  React.useEffect(() => {
    const updatedData: Partial<PropertyConfig> = {
      ...data,
      equalUnits,
      unitsPerFloor: equalUnits ? unitsPerFloor : customUnits,
    };
    onUpdate(updatedData);
  }, [equalUnits, unitsPerFloor, customUnits, data, onUpdate]);

  // Initialize custom units array when switching to custom mode
  React.useEffect(() => {
    if (!equalUnits && customUnits.length === 0) {
      const initialCustomUnits = floorsPerBuilding.map(floors => 
        Array(floors).fill(4)
      );
      setCustomUnits(initialCustomUnits);
    }
  }, [equalUnits, floorsPerBuilding, customUnits.length]);

  const updateCustomUnit = (buildingIndex: number, floorIndex: number, value: number) => {
    const updated = [...customUnits];
    if (!updated[buildingIndex]) {
      updated[buildingIndex] = [];
    }
    updated[buildingIndex][floorIndex] = value;
    setCustomUnits(updated);
  };

  const getTotalUnits = () => {
    if (equalUnits) {
      const totalFloors = floorsPerBuilding.reduce((sum, floors) => sum + floors, 0);
      return totalFloors * unitsPerFloor;
    } else {
      return customUnits.reduce((buildingSum, building) => 
        buildingSum + (building?.reduce((floorSum, units) => floorSum + units, 0) || 0), 0
      );
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Unit Configuration</h3>
        
        <div className="space-y-4">
          <div className="space-y-3">
            <Label>Do all floors have the same number of units?</Label>
            <RadioGroup 
              value={equalUnits ? 'yes' : 'no'}
              onValueChange={(value) => setEqualUnits(value === 'yes')}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="units-equal-yes" />
                <Label htmlFor="units-equal-yes">Yes, all floors have equal units</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="units-equal-no" />
                <Label htmlFor="units-equal-no">No, different number of units per floor</Label>
              </div>
            </RadioGroup>
          </div>

          {equalUnits ? (
            <div className="space-y-2">
              <Label htmlFor="unitsPerFloor">Number of Units per Floor</Label>
              <Input
                id="unitsPerFloor"
                type="number"
                min="1"
                value={unitsPerFloor}
                onChange={(e) => setUnitsPerFloor(parseInt(e.target.value) || 1)}
                placeholder="e.g., 4"
                className="max-w-xs"
              />
              <p className="text-sm text-muted-foreground">
                This will apply to all floors in all buildings
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <Label>Units per Floor (by Building)</Label>
              {floorsPerBuilding.map((floors, buildingIndex) => (
                <Card key={buildingIndex} className="p-4 bg-muted/20">
                  <h4 className="font-medium mb-3">Building {buildingIndex + 1} ({floors} floors)</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {Array.from({ length: floors }, (_, floorIndex) => (
                      <div key={floorIndex} className="space-y-1">
                        <Label className="text-xs">Floor {floorIndex + 1}</Label>
                        <Input
                          type="number"
                          min="1"
                          value={customUnits[buildingIndex]?.[floorIndex] || 4}
                          onChange={(e) => updateCustomUnit(buildingIndex, floorIndex, parseInt(e.target.value) || 1)}
                          className="text-sm"
                          placeholder="Units"
                        />
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </Card>

      <Card className="p-6 bg-muted/30">
        <h3 className="font-semibold mb-4">Unit Summary</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-2 text-sm">
            <p><strong>Total Buildings:</strong> {numberOfBuildings}</p>
            <p><strong>Total Floors:</strong> {floorsPerBuilding.reduce((sum, floors) => sum + floors, 0)}</p>
            <p><strong>Total Units:</strong> {getTotalUnits()}</p>
          </div>
          
          <div className="space-y-2 text-sm">
            <p><strong>Configuration:</strong></p>
            {equalUnits ? (
              <p className="text-muted-foreground">Equal units per floor: {unitsPerFloor}</p>
            ) : (
              <p className="text-muted-foreground">Custom units per floor (varies by building/floor)</p>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};