import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { CreditCard, Plus, Trash2 } from 'lucide-react';
import { BillingRule } from '@/types/wizard';

interface BillingRulesProps {
  data?: BillingRule[];
  onUpdate: (data: BillingRule[]) => void;
}

const defaultRules = [
  { name: 'CAM Charges', type: 'CAM', frequency: 'monthly', amount: 2500 },
  { name: 'Sinking Fund', type: 'SinkingFund', frequency: 'quarterly', amount: 1000 },
  { name: 'Parking Charges', type: 'Parking', frequency: 'monthly', amount: 500 },
  { name: 'Water Charges', type: 'Utilities', frequency: 'monthly', amount: 300 },
  { name: 'Security Charges', type: 'Security', frequency: 'monthly', amount: 800 },
];

export const BillingRules: React.FC<BillingRulesProps> = ({ data, onUpdate }) => {
  const [rules, setRules] = React.useState<BillingRule[]>(data || []);
  const [newRule, setNewRule] = React.useState({
    name: '',
    type: '',
    frequency: 'monthly' as 'monthly' | 'quarterly' | 'annually',
    amount: '',
    dueDate: '',
    penalty: ''
  });

  React.useEffect(() => {
    onUpdate(rules);
  }, [rules, onUpdate]);

  const addRule = () => {
    if (newRule.name.trim() && newRule.type.trim() && newRule.amount.trim()) {
      const rule: BillingRule = {
        id: Date.now().toString(),
        name: newRule.name.trim(),
        type: newRule.type.trim(),
        frequency: newRule.frequency,
        amount: parseFloat(newRule.amount),
        dueDate: newRule.dueDate ? parseInt(newRule.dueDate) : undefined,
        penalty: newRule.penalty ? parseFloat(newRule.penalty) : undefined
      };
      setRules(prev => [...prev, rule]);
      setNewRule({
        name: '',
        type: '',
        frequency: 'monthly',
        amount: '',
        dueDate: '',
        penalty: ''
      });
    }
  };

  const addDefaultRule = (defaultRule: typeof defaultRules[0]) => {
    const rule: BillingRule = {
      id: Date.now().toString(),
      name: defaultRule.name,
      type: defaultRule.type,
      frequency: defaultRule.frequency as 'monthly' | 'quarterly' | 'annually',
      amount: defaultRule.amount
    };
    setRules(prev => [...prev, rule]);
  };

  const removeRule = (ruleId: string) => {
    setRules(prev => prev.filter(r => r.id !== ruleId));
  };

  const totalMonthlyAmount = rules
    .filter(r => r.frequency === 'monthly')
    .reduce((sum, r) => sum + r.amount, 0);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <CreditCard className="w-12 h-12 text-primary mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          Billing Rules Configuration
        </h3>
        <p className="text-muted-foreground">
          Define billing rules, charges, frequencies, and due dates for your property management.
        </p>
      </div>

      {/* Quick Add Default Rules */}
      <Card className="p-6">
        <h4 className="font-medium mb-4">Quick Add Common Rules</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {defaultRules.map((rule) => (
            <Button
              key={rule.name}
              variant="outline"
              onClick={() => addDefaultRule(rule)}
              disabled={rules.some(r => r.name === rule.name)}
              className="justify-start text-left h-auto p-3"
            >
              <div>
                <div className="font-medium">{rule.name}</div>
                <div className="text-sm text-muted-foreground">
                  ₹{rule.amount.toLocaleString()} / {rule.frequency}
                </div>
              </div>
            </Button>
          ))}
        </div>
      </Card>

      {/* Add Custom Rule Form */}
      <Card className="p-6">
        <h4 className="font-medium mb-4">Add Custom Billing Rule</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="ruleName">Rule Name *</Label>
            <Input
              id="ruleName"
              value={newRule.name}
              onChange={(e) => setNewRule(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Maintenance Charge"
            />
          </div>
          <div>
            <Label htmlFor="ruleType">Type *</Label>
            <Input
              id="ruleType"
              value={newRule.type}
              onChange={(e) => setNewRule(prev => ({ ...prev, type: e.target.value }))}
              placeholder="e.g., Maintenance, Utilities"
            />
          </div>
          <div>
            <Label htmlFor="frequency">Frequency</Label>
            <select
              id="frequency"
              value={newRule.frequency}
              onChange={(e) => setNewRule(prev => ({ ...prev, frequency: e.target.value as any }))}
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="annually">Annually</option>
            </select>
          </div>
          <div>
            <Label htmlFor="amount">Amount *</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              value={newRule.amount}
              onChange={(e) => setNewRule(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="0.00"
            />
          </div>
          <div>
            <Label htmlFor="dueDate">Due Date (Day of Month)</Label>
            <Input
              id="dueDate"
              type="number"
              min="1"
              max="31"
              value={newRule.dueDate}
              onChange={(e) => setNewRule(prev => ({ ...prev, dueDate: e.target.value }))}
              placeholder="e.g., 10"
            />
          </div>
          <div>
            <Label htmlFor="penalty">Late Fee (%)</Label>
            <Input
              id="penalty"
              type="number"
              step="0.1"
              value={newRule.penalty}
              onChange={(e) => setNewRule(prev => ({ ...prev, penalty: e.target.value }))}
              placeholder="e.g., 2.5"
            />
          </div>
        </div>
        <Button onClick={addRule} className="mt-4">
          <Plus className="w-4 h-4 mr-2" />
          Add Billing Rule
        </Button>
      </Card>

      {/* Rules Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-primary">{rules.length}</div>
          <div className="text-sm text-muted-foreground">Total Rules</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-success">₹{totalMonthlyAmount.toLocaleString()}</div>
          <div className="text-sm text-muted-foreground">Monthly Charges</div>
        </Card>
      </div>

      {/* Rules List */}
      <div className="space-y-4">
        <h4 className="font-medium">Configured Billing Rules</h4>
        
        {rules.length === 0 ? (
          <Card className="p-8 text-center">
            <CreditCard className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No billing rules configured</p>
            <p className="text-sm text-muted-foreground mt-1">
              Add rules using the quick options above or create custom rules
            </p>
          </Card>
        ) : (
          <div className="space-y-3">
            {rules.map((rule) => (
              <Card key={rule.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h5 className="font-medium">{rule.name}</h5>
                      <Badge variant="outline">{rule.type}</Badge>
                      <Badge variant="secondary">{rule.frequency}</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Amount: ₹{rule.amount.toLocaleString()}
                      {rule.dueDate && ` • Due: ${rule.dueDate}th of month`}
                      {rule.penalty && ` • Late fee: ${rule.penalty}%`}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeRule(rule.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Card className="p-4 bg-accent/10 border-accent/20">
        <p className="text-sm text-muted-foreground">
          <strong>Important:</strong> These billing rules will be used to generate monthly/periodic bills. 
          You can modify these rules later from the billing management section.
        </p>
      </Card>
    </div>
  );
};