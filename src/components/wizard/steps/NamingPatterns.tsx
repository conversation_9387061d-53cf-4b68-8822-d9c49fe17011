import React from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PropertyConfig } from '@/types/wizard';

interface NamingPatternsProps {
  data?: Partial<PropertyConfig>;
  onUpdate: (data: Partial<PropertyConfig>) => void;
}

const buildingPatterns = [
  { value: 'letter', label: 'Letters (A, B, C...)', example: 'Building A' },
  { value: 'number', label: 'Numbers (1, 2, 3...)', example: 'Building 1' },
  { value: 'name', label: 'Custom Names', example: 'Tower East' },
  { value: 'block', label: 'Block System', example: 'Block A' },
];

const floorPatterns = [
  { value: 'number', label: 'Numbers (1, 2, 3...)', example: 'Floor 1' },
  { value: 'ground', label: 'Ground + Numbers', example: 'Ground, 1st, 2nd...' },
  { value: 'letter', label: 'Letters (A, B, C...)', example: 'Floor A' },
];

const unitPatterns = [
  { value: 'sequential', label: 'Sequential (101, 102, 103...)', example: '101' },
  { value: 'building-floor', label: 'Building-Floor (A101, A102...)', example: 'A101' },
  { value: 'letter-number', label: 'Letter-Number (1A, 1B, 1C...)', example: '1A' },
  { value: 'custom', label: 'Custom Pattern', example: 'Apt-101' },
];

export const NamingPatterns: React.FC<NamingPatternsProps> = ({ data, onUpdate }) => {
  const [buildingPattern, setBuildingPattern] = React.useState(data?.buildingNamingPattern || 'letter');
  const [floorPattern, setFloorPattern] = React.useState(data?.floorNamingPattern || 'number');
  const [unitPattern, setUnitPattern] = React.useState(data?.unitNamingPattern || 'sequential');

  React.useEffect(() => {
    onUpdate({
      ...data,
      buildingNamingPattern: buildingPattern,
      floorNamingPattern: floorPattern,
      unitNamingPattern: unitPattern,
    });
  }, [buildingPattern, floorPattern, unitPattern, data, onUpdate]);

  const getExample = () => {
    let building = '';
    let floor = '';
    let unit = '';

    switch (buildingPattern) {
      case 'letter': building = 'A'; break;
      case 'number': building = '1'; break;
      case 'name': building = 'Tower East'; break;
      case 'block': building = 'Block A'; break;
    }

    switch (floorPattern) {
      case 'number': floor = '3'; break;
      case 'ground': floor = '3rd'; break;
      case 'letter': floor = 'C'; break;
    }

    switch (unitPattern) {
      case 'sequential': unit = '301'; break;
      case 'building-floor': unit = 'A301'; break;
      case 'letter-number': unit = '3A'; break;
      case 'custom': unit = 'Apt-301'; break;
    }

    return { building, floor, unit };
  };

  const example = getExample();

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Naming Patterns</h3>
        <p className="text-muted-foreground mb-6 text-sm">
          Choose how buildings, floors, and units will be named throughout your property management system.
        </p>
        
        <div className="space-y-6">
          <div className="space-y-2">
            <Label>Building Naming Pattern</Label>
            <Select value={buildingPattern} onValueChange={setBuildingPattern}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {buildingPatterns.map((pattern) => (
                  <SelectItem key={pattern.value} value={pattern.value}>
                    <div className="flex justify-between items-center w-full">
                      <span>{pattern.label}</span>
                      <span className="text-xs text-muted-foreground ml-4">
                        {pattern.example}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Floor Naming Pattern</Label>
            <Select value={floorPattern} onValueChange={setFloorPattern}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {floorPatterns.map((pattern) => (
                  <SelectItem key={pattern.value} value={pattern.value}>
                    <div className="flex justify-between items-center w-full">
                      <span>{pattern.label}</span>
                      <span className="text-xs text-muted-foreground ml-4">
                        {pattern.example}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Unit Naming Pattern</Label>
            <Select value={unitPattern} onValueChange={setUnitPattern}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {unitPatterns.map((pattern) => (
                  <SelectItem key={pattern.value} value={pattern.value}>
                    <div className="flex justify-between items-center w-full">
                      <span>{pattern.label}</span>
                      <span className="text-xs text-muted-foreground ml-4">
                        {pattern.example}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-accent/10">
        <h3 className="font-semibold mb-4">Preview Example</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-2 text-sm">
            <span className="font-medium">Building:</span>
            <span className="px-2 py-1 bg-background rounded border">{example.building}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <span className="font-medium">Floor:</span>
            <span className="px-2 py-1 bg-background rounded border">{example.floor}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <span className="font-medium">Unit:</span>
            <span className="px-2 py-1 bg-background rounded border">{example.unit}</span>
          </div>
          <div className="mt-4 p-3 bg-primary/10 rounded">
            <p className="text-sm font-medium">Complete Address Example:</p>
            <p className="text-sm text-muted-foreground mt-1">
              Unit {example.unit}, {floorPattern === 'ground' ? example.floor + ' Floor' : 'Floor ' + example.floor}, {example.building}
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};