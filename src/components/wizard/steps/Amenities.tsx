import React from 'react';
import { ParkingAmenities } from './ParkingAmenities';
import { PropertyConfig } from '@/types/wizard';

interface AmenitiesProps {
  data?: Partial<PropertyConfig>;
  onUpdate: (data: Partial<PropertyConfig>) => void;
}

export const Amenities: React.FC<AmenitiesProps> = ({ data, onUpdate }) => {
  return (
    <ParkingAmenities 
      data={data} 
      onUpdate={onUpdate} 
      tenantType="individual" 
    />
  );
};