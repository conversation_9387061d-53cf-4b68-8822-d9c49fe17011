import React from 'react';
import { useForm } from 'react-hook-form';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Button } from '@/components/ui/button';
import { Trash2, Plus } from 'lucide-react';
import { PropertyConfig } from '@/types/wizard';

interface BuildingsFloorsProps {
  data?: Partial<PropertyConfig>;
  onUpdate: (data: Partial<PropertyConfig>) => void;
}

export const BuildingsFloors: React.FC<BuildingsFloorsProps> = ({ data, onUpdate }) => {
  const { register, watch, setValue } = useForm<Partial<PropertyConfig>>({
    defaultValues: data || { equalFloors: true },
  });

  const formData = watch();
  const [customFloors, setCustomFloors] = React.useState<number[]>(
    Array.isArray(data?.floorsPerBuilding) 
      ? data.floorsPerBuilding 
      : Array(data?.numberOfBuildings || 1).fill(1)
  );

  React.useEffect(() => {
    if (formData.equalFloors !== undefined) {
      const updatedData = { ...formData };
      
      if (formData.equalFloors) {
        updatedData.floorsPerBuilding = formData.floorsPerBuilding as number;
      } else {
        updatedData.floorsPerBuilding = customFloors;
      }
      
      onUpdate(updatedData);
    }
  }, [formData, customFloors, onUpdate]);

  const updateCustomFloor = (index: number, value: number) => {
    const updated = [...customFloors];
    updated[index] = value;
    setCustomFloors(updated);
  };

  const numberOfBuildings = data?.numberOfBuildings || 1;

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Floor Configuration</h3>
        
        <div className="space-y-4">
          <div className="space-y-3">
            <Label>Do all buildings have the same number of floors?</Label>
            <RadioGroup 
              value={formData.equalFloors ? 'yes' : 'no'}
              onValueChange={(value) => setValue('equalFloors', value === 'yes')}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="equal-yes" />
                <Label htmlFor="equal-yes">Yes, all buildings have equal floors</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="equal-no" />
                <Label htmlFor="equal-no">No, different number of floors per building</Label>
              </div>
            </RadioGroup>
          </div>

          {formData.equalFloors ? (
            <div className="space-y-2">
              <Label htmlFor="floorsPerBuilding">Number of Floors per Building</Label>
              <Input
                id="floorsPerBuilding"
                type="number"
                min="1"
                {...register('floorsPerBuilding', { 
                  valueAsNumber: true,
                  required: true 
                })}
                placeholder="e.g., 10"
                className="max-w-xs"
              />
              <p className="text-sm text-muted-foreground">
                This will apply to all {numberOfBuildings} building(s)
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <Label>Floors per Building</Label>
              <div className="grid gap-4">
                {Array.from({ length: numberOfBuildings }, (_, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <Label className="min-w-[120px]">Building {index + 1}:</Label>
                    <Input
                      type="number"
                      min="1"
                      value={customFloors[index] || 1}
                      onChange={(e) => updateCustomFloor(index, parseInt(e.target.value) || 1)}
                      className="max-w-[100px]"
                      placeholder="Floors"
                    />
                    <span className="text-sm text-muted-foreground">floors</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </Card>

      <Card className="p-6 bg-muted/30">
        <h3 className="font-semibold mb-4">Summary</h3>
        <div className="space-y-2 text-sm">
          <p><strong>Total Buildings:</strong> {numberOfBuildings}</p>
          {formData.equalFloors ? (
            <p><strong>Floors per Building:</strong> {formData.floorsPerBuilding || 'Not set'}</p>
          ) : (
            <div>
              <p><strong>Floors Configuration:</strong></p>
              <ul className="ml-4 mt-2 space-y-1">
                {customFloors.map((floors, index) => (
                  <li key={index}>Building {index + 1}: {floors} floors</li>
                ))}
              </ul>
            </div>
          )}
          <p><strong>Total Floors:</strong> {
            formData.equalFloors 
              ? (typeof formData.floorsPerBuilding === 'number' ? formData.floorsPerBuilding * numberOfBuildings : 0)
              : customFloors.reduce((sum, floors) => sum + floors, 0)
          }</p>
        </div>
      </Card>
    </div>
  );
};