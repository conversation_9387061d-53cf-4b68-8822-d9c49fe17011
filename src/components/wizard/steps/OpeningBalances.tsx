import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Calculator, Upload, Download, Plus } from 'lucide-react';
import { OpeningBalance } from '@/types/wizard';

interface OpeningBalancesProps {
  data?: OpeningBalance[];
  onUpdate: (data: OpeningBalance[]) => void;
}

export const OpeningBalances: React.FC<OpeningBalancesProps> = ({ data, onUpdate }) => {
  const [balances, setBalances] = React.useState<OpeningBalance[]>(data || []);
  const [newBalance, setNewBalance] = React.useState({
    unitNumber: '',
    balance: '',
    type: 'credit' as 'credit' | 'debit'
  });

  React.useEffect(() => {
    onUpdate(balances);
  }, [balances, onUpdate]);

  const addBalance = () => {
    if (newBalance.unitNumber.trim() && newBalance.balance.trim()) {
      const balance: OpeningBalance = {
        unitId: Date.now().toString(),
        unitNumber: newBalance.unitNumber.trim(),
        balance: parseFloat(newBalance.balance),
        type: newBalance.type
      };
      setBalances(prev => [...prev, balance]);
      setNewBalance({
        unitNumber: '',
        balance: '',
        type: 'credit'
      });
    }
  };

  const removeBalance = (unitId: string) => {
    setBalances(prev => prev.filter(b => b.unitId !== unitId));
  };

  const totalCredit = balances.filter(b => b.type === 'credit').reduce((sum, b) => sum + b.balance, 0);
  const totalDebit = balances.filter(b => b.type === 'debit').reduce((sum, b) => sum + b.balance, 0);
  const netBalance = totalCredit - totalDebit;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <Calculator className="w-12 h-12 text-primary mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          Opening Balances
        </h3>
        <p className="text-muted-foreground">
          Set initial account balances for unit ledgers. You can upload via CSV/Excel or enter manually.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-success">{totalCredit.toLocaleString()}</div>
          <div className="text-sm text-muted-foreground">Total Credits</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-destructive">{totalDebit.toLocaleString()}</div>
          <div className="text-sm text-muted-foreground">Total Debits</div>
        </Card>
        <Card className="p-4 text-center">
          <div className={`text-2xl font-bold ${netBalance >= 0 ? 'text-success' : 'text-destructive'}`}>
            {netBalance.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Net Balance</div>
        </Card>
      </div>

      {/* Bulk Upload Options */}
      <Card className="p-6">
        <h4 className="font-medium mb-4">Bulk Import Options</h4>
        <div className="flex gap-4">
          <Button variant="outline" className="flex-1">
            <Download className="w-4 h-4 mr-2" />
            Download Template
          </Button>
          <Button variant="outline" className="flex-1">
            <Upload className="w-4 h-4 mr-2" />
            Upload CSV/Excel
          </Button>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Download the Excel template, fill in your data, and upload it for bulk import.
        </p>
      </Card>

      {/* Manual Entry Form */}
      <Card className="p-6">
        <h4 className="font-medium mb-4">Add Opening Balance</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="unitNumber">Unit Number *</Label>
            <Input
              id="unitNumber"
              value={newBalance.unitNumber}
              onChange={(e) => setNewBalance(prev => ({ ...prev, unitNumber: e.target.value }))}
              placeholder="e.g., A-101"
            />
          </div>
          <div>
            <Label htmlFor="balance">Amount *</Label>
            <Input
              id="balance"
              type="number"
              step="0.01"
              value={newBalance.balance}
              onChange={(e) => setNewBalance(prev => ({ ...prev, balance: e.target.value }))}
              placeholder="0.00"
            />
          </div>
          <div>
            <Label htmlFor="balanceType">Type</Label>
            <select
              id="balanceType"
              value={newBalance.type}
              onChange={(e) => setNewBalance(prev => ({ ...prev, type: e.target.value as 'credit' | 'debit' }))}
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="credit">Credit (Advance/Deposit)</option>
              <option value="debit">Debit (Outstanding Due)</option>
            </select>
          </div>
          <div className="flex items-end">
            <Button onClick={addBalance} className="w-full">
              <Plus className="w-4 h-4 mr-2" />
              Add Balance
            </Button>
          </div>
        </div>
      </Card>

      {/* Balances List */}
      <div className="space-y-4">
        <h4 className="font-medium">Opening Balances ({balances.length} entries)</h4>
        
        {balances.length === 0 ? (
          <Card className="p-8 text-center">
            <Calculator className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No opening balances set</p>
            <p className="text-sm text-muted-foreground mt-1">
              Add balances manually or import from CSV/Excel template
            </p>
          </Card>
        ) : (
          <Card className="p-4">
            <div className="space-y-3">
              {balances.map((balance) => (
                <div key={balance.unitId} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="font-medium">Unit {balance.unitNumber}</div>
                    <Badge variant={balance.type === 'credit' ? 'default' : 'destructive'}>
                      {balance.type === 'credit' ? 'Credit' : 'Debit'}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className={`font-medium ${balance.type === 'credit' ? 'text-success' : 'text-destructive'}`}>
                      ₹{balance.balance.toLocaleString()}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeBalance(balance.unitId)}
                      className="text-destructive hover:text-destructive"
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}
      </div>

      <Card className="p-4 bg-accent/10 border-accent/20">
        <p className="text-sm text-muted-foreground">
          <strong>Note:</strong> Opening balances help track existing credits/debits from previous billing cycles. 
          This step is optional and can be completed later.
        </p>
      </Card>
    </div>
  );
};