import React from 'react';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AdminProfile as AdminProfileType } from '@/types/wizard';

interface AdminProfileProps {
  data?: AdminProfileType;
  onUpdate: (data: AdminProfileType) => void;
}

export const AdminProfile: React.FC<AdminProfileProps> = ({ data, onUpdate }) => {
  const { register, watch } = useForm<AdminProfileType>({
    defaultValues: data || {},
  });

  const formData = watch();

  React.useEffect(() => {
    if (formData.firstName && formData.email) {
      onUpdate(formData);
    }
  }, [formData, onUpdate]);

  return (
    <div className="space-y-8">
      {/* Personal Information Section */}
      <div className="space-y-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-1 h-6 bg-primary rounded-full" />
          <h3 className="text-xl font-bold text-foreground">Personal Information</h3>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <Label htmlFor="firstName" className="text-base">
              First Name <span className="text-primary">*</span>
            </Label>
            <Input
              id="firstName"
              {...register('firstName', { required: true })}
              placeholder="Enter your first name"
              className="input-enhanced"
            />
          </div>

          <div className="space-y-3">
            <Label htmlFor="lastName" className="text-base">Last Name</Label>
            <Input
              id="lastName"
              {...register('lastName')}
              placeholder="Enter your last name"
              className="input-enhanced"
            />
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="space-y-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-1 h-6 bg-primary rounded-full" />
          <h3 className="text-xl font-bold text-foreground">Contact Information</h3>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <Label htmlFor="email" className="text-base">
              Email Address <span className="text-primary">*</span>
            </Label>
            <Input
              id="email"
              type="email"
              {...register('email', { required: true })}
              placeholder="<EMAIL>"
              className="input-enhanced"
            />
          </div>

          <div className="space-y-3">
            <Label htmlFor="phone" className="text-base">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              {...register('phone')}
              placeholder="+91 98765 43210"
              className="input-enhanced"
            />
          </div>
        </div>
      </div>

      {/* Organization Section */}
      <div className="space-y-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-1 h-6 bg-primary rounded-full" />
          <h3 className="text-xl font-bold text-foreground">Organization</h3>
        </div>

        <div className="space-y-3">
          <Label htmlFor="companyName" className="text-base">Company/Organization Name</Label>
          <Input
            id="companyName"
            {...register('companyName')}
            placeholder="Enter company or organization name"
            className="input-enhanced"
          />
        </div>
      </div>

      {/* Address Section */}
      <div className="card-enhanced p-8 bg-primary-light/50 border-primary/20">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-1 h-6 bg-primary rounded-full" />
          <h3 className="text-xl font-bold text-foreground">Address Information</h3>
        </div>

        <div className="space-y-6">
          <div className="space-y-3">
            <Label htmlFor="address" className="text-base">Street Address</Label>
            <Textarea
              id="address"
              {...register('address')}
              placeholder="Enter complete address"
              className="min-h-[100px] rounded-xl border-2 border-input bg-background px-4 py-3 text-base transition-all duration-300 focus:border-primary focus:ring-2 focus:ring-primary/20"
            />
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <Label htmlFor="city" className="text-base">City</Label>
              <Input
                id="city"
                {...register('city')}
                placeholder="City"
                className="input-enhanced"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="state" className="text-base">State</Label>
              <Input
                id="state"
                {...register('state')}
                placeholder="State"
                className="input-enhanced"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="pincode" className="text-base">PIN Code</Label>
              <Input
                id="pincode"
                {...register('pincode')}
                placeholder="PIN Code"
                className="input-enhanced"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};