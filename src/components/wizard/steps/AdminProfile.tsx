import React from 'react';
import { useForm } from 'react-hook-form';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AdminProfile as AdminProfileType } from '@/types/wizard';

interface AdminProfileProps {
  data?: AdminProfileType;
  onUpdate: (data: AdminProfileType) => void;
}

export const AdminProfile: React.FC<AdminProfileProps> = ({ data, onUpdate }) => {
  const { register, handleSubmit, watch } = useForm<AdminProfileType>({
    defaultValues: data || {},
  });

  const formData = watch();

  React.useEffect(() => {
    if (formData.firstName && formData.email) {
      onUpdate(formData);
    }
  }, [formData, onUpdate]);

  return (
    <div className="space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name *</Label>
          <Input
            id="firstName"
            {...register('firstName', { required: true })}
            placeholder="Enter your first name"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            {...register('lastName')}
            placeholder="Enter your last name"
          />
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            {...register('email', { required: true })}
            placeholder="<EMAIL>"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            type="tel"
            {...register('phone')}
            placeholder="+91 98765 43210"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="companyName">Company/Organization Name</Label>
        <Input
          id="companyName"
          {...register('companyName')}
          placeholder="Enter company or organization name"
        />
      </div>

      <Card className="p-6 bg-muted/30">
        <h3 className="font-semibold mb-4">Address Information</h3>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="address">Street Address</Label>
            <Textarea
              id="address"
              {...register('address')}
              placeholder="Enter complete address"
              className="min-h-[80px]"
            />
          </div>
          
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                {...register('city')}
                placeholder="City"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="state">State</Label>
              <Input
                id="state"
                {...register('state')}
                placeholder="State"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="pincode">PIN Code</Label>
              <Input
                id="pincode"
                {...register('pincode')}
                placeholder="PIN Code"
              />
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};