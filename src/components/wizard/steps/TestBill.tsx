import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { FileText, Download, CheckCircle, AlertTriangle } from 'lucide-react';
import { TestBill as TestBillType, BillingRule } from '@/types/wizard';

interface TestBillProps {
  data?: TestBillType;
  onUpdate: (data: TestBillType) => void;
  billingRules?: BillingRule[];
}

export const TestBill: React.FC<TestBillProps> = ({ data, onUpdate, billingRules = [] }) => {
  const [selectedUnit, setSelectedUnit] = React.useState('');
  const [testBill, setTestBill] = React.useState<TestBillType | null>(data || null);
  const [isGenerating, setIsGenerating] = React.useState(false);

  const generateTestBill = async () => {
    if (!selectedUnit.trim()) return;

    setIsGenerating(true);
    
    // Simulate bill generation
    setTimeout(() => {
      const bill: TestBillType = {
        unitId: Date.now().toString(),
        unitNumber: selectedUnit,
        charges: billingRules,
        total: billingRules.reduce((sum, rule) => sum + rule.amount, 0),
        generatedAt: new Date().toISOString()
      };
      
      setTestBill(bill);
      onUpdate(bill);
      setIsGenerating(false);
    }, 2000);
  };

  const downloadBill = () => {
    if (!testBill) return;
    
    // Create a simple text representation of the bill
    const billContent = `
PROPERTY MANAGEMENT SYSTEM
Test Bill for Unit: ${testBill.unitNumber}
Generated: ${new Date(testBill.generatedAt).toLocaleDateString()}

CHARGES:
${testBill.charges.map(charge => 
  `${charge.name.padEnd(25)} ₹${charge.amount.toLocaleString()}`
).join('\n')}

${''.padEnd(40, '-')}
TOTAL AMOUNT: ₹${testBill.total.toLocaleString()}
${''.padEnd(40, '-')}

This is a test bill generated during setup.
    `;

    const blob = new Blob([billContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `test-bill-${testBill.unitNumber}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <FileText className="w-12 h-12 text-primary mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          Test Bill Generation
        </h3>
        <p className="text-muted-foreground">
          Generate a test bill to validate your billing configuration and calculation logic.
        </p>
      </div>

      {/* Billing Rules Summary */}
      <Card className="p-6">
        <h4 className="font-medium mb-4">Billing Rules Summary</h4>
        {billingRules.length === 0 ? (
          <div className="text-center py-4">
            <AlertTriangle className="w-8 h-8 text-warning mx-auto mb-2" />
            <p className="text-muted-foreground">No billing rules configured</p>
            <p className="text-sm text-muted-foreground">
              Please configure billing rules in the previous step to generate test bills
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {billingRules.map((rule) => (
              <div key={rule.id} className="flex justify-between items-center p-2 bg-muted rounded">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{rule.name}</span>
                  <Badge variant="outline">{rule.frequency}</Badge>
                </div>
                <span className="font-medium">₹{rule.amount.toLocaleString()}</span>
              </div>
            ))}
            <div className="flex justify-between items-center p-2 bg-primary/10 rounded font-medium">
              <span>Total Monthly Amount</span>
              <span>₹{billingRules.filter(r => r.frequency === 'monthly').reduce((sum, r) => sum + r.amount, 0).toLocaleString()}</span>
            </div>
          </div>
        )}
      </Card>

      {/* Test Bill Generation */}
      <Card className="p-6">
        <h4 className="font-medium mb-4">Generate Test Bill</h4>
        <div className="flex gap-4 mb-4">
          <div className="flex-1">
            <Label htmlFor="testUnit">Select Unit for Test Bill</Label>
            <Input
              id="testUnit"
              value={selectedUnit}
              onChange={(e) => setSelectedUnit(e.target.value)}
              placeholder="e.g., A-101, B-205"
              disabled={isGenerating}
            />
          </div>
          <div className="flex items-end">
            <Button 
              onClick={generateTestBill}
              disabled={!selectedUnit.trim() || billingRules.length === 0 || isGenerating}
              className="min-w-[120px]"
            >
              {isGenerating ? 'Generating...' : 'Generate Bill'}
            </Button>
          </div>
        </div>
      </Card>

      {/* Test Bill Preview */}
      {testBill && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-success" />
              Test Bill Generated Successfully
            </h4>
            <Button onClick={downloadBill} variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>
          
          <div className="border rounded-lg p-6 bg-background">
            <div className="text-center mb-6">
              <h5 className="text-lg font-bold">PROPERTY MANAGEMENT SYSTEM</h5>
              <p className="text-sm text-muted-foreground">Test Bill</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
              <div>
                <strong>Unit Number:</strong> {testBill.unitNumber}
              </div>
              <div>
                <strong>Generated:</strong> {new Date(testBill.generatedAt).toLocaleDateString()}
              </div>
            </div>
            
            <div className="space-y-3 mb-6">
              <h6 className="font-medium border-b pb-2">CHARGES BREAKDOWN</h6>
              {testBill.charges.map((charge) => (
                <div key={charge.id} className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <span>{charge.name}</span>
                    <Badge variant="outline" className="text-xs">{charge.frequency}</Badge>
                  </div>
                  <span>₹{charge.amount.toLocaleString()}</span>
                </div>
              ))}
            </div>
            
            <div className="border-t pt-3">
              <div className="flex justify-between items-center text-lg font-bold">
                <span>TOTAL AMOUNT</span>
                <span>₹{testBill.total.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <div className="mt-4 p-4 bg-success/10 border border-success/20 rounded-lg">
            <p className="text-sm text-success-foreground">
              <strong>✓ Bill calculation verified!</strong> Your billing rules are working correctly. 
              The generated bill shows proper charge calculation and formatting.
            </p>
          </div>
        </Card>
      )}

      <Card className="p-4 bg-accent/10 border-accent/20">
        <p className="text-sm text-muted-foreground">
          <strong>Note:</strong> This test bill validates your billing configuration. 
          Once setup is complete, actual bills will be generated according to your billing schedule.
        </p>
      </Card>
    </div>
  );
};