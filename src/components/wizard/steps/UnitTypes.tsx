import React from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Trash2 } from 'lucide-react';
import { PropertyConfig, UnitType } from '@/types/wizard';

interface UnitTypesProps {
  data?: Partial<PropertyConfig>;
  onUpdate: (data: Partial<PropertyConfig>) => void;
  tenantType?: string;
}

const defaultResidentialTypes = [
  '1 BHK',
  '2 BHK', 
  '3 BHK',
  '4 BHK',
  'Studio',
  'Duplex',
  'Penthouse',
];

const defaultCommercialTypes = [
  'Office Space',
  'Retail Shop',
  'Restaurant',
  'Warehouse',
  'Showroom',
  'Clinic',
  'Co-working Space',
];

export const UnitTypes: React.FC<UnitTypesProps> = ({ data, onUpdate, tenantType }) => {
  const defaultTypes = tenantType === 'commercial' ? defaultCommercialTypes : defaultResidentialTypes;
  // Convert string array to UnitType array for compatibility
  const existingTypes = data?.unitTypes || [];
  const convertedTypes = Array.isArray(existingTypes) && existingTypes.length > 0 && typeof existingTypes[0] === 'string' 
    ? (existingTypes as unknown as string[]).map((name, index) => ({
        id: `default-${index}`,
        name,
        category: (tenantType === 'commercial' ? 'commercial' : 'residential') as 'commercial' | 'residential',
        description: ''
      }))
    : existingTypes as UnitType[];
  
  const [selectedTypes, setSelectedTypes] = React.useState<UnitType[]>(convertedTypes);
  const [customType, setCustomType] = React.useState('');

  React.useEffect(() => {
    onUpdate({ ...data, unitTypes: selectedTypes });
  }, [selectedTypes, data, onUpdate]);

  const toggleType = (typeName: string) => {
    setSelectedTypes(prev => {
      const exists = prev.find(t => t.name === typeName);
      if (exists) {
        return prev.filter(t => t.name !== typeName);
      } else {
        const newType: UnitType = {
          id: `type-${Date.now()}`,
          name: typeName,
          category: (tenantType === 'commercial' ? 'commercial' : 'residential') as 'commercial' | 'residential',
          description: ''
        };
        return [...prev, newType];
      }
    });
  };

  const addCustomType = () => {
    if (customType.trim() && !selectedTypes.find(t => t.name === customType.trim())) {
      const newType: UnitType = {
        id: `custom-${Date.now()}`,
        name: customType.trim(),
        category: (tenantType === 'commercial' ? 'commercial' : 'residential') as 'commercial' | 'residential',
        description: ''
      };
      setSelectedTypes(prev => [...prev, newType]);
      setCustomType('');
    }
  };

  const removeCustomType = (typeName: string) => {
    if (!defaultTypes.includes(typeName)) {
      setSelectedTypes(prev => prev.filter(t => t.name !== typeName));
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="font-semibold mb-4">
          {tenantType === 'commercial' ? 'Commercial Unit Types' : 'Residential Unit Types'}
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label className="text-base mb-3 block">Select standard unit types:</Label>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
              {defaultTypes.map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox
                    id={type}
                    checked={selectedTypes.some(t => t.name === type)}
                    onCheckedChange={() => toggleType(type)}
                  />
                  <Label htmlFor={type} className="text-sm cursor-pointer">
                    {type}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="border-t pt-4">
            <Label className="text-base mb-3 block">Add custom unit type:</Label>
            <div className="flex space-x-2">
              <Input
                value={customType}
                onChange={(e) => setCustomType(e.target.value)}
                placeholder="Enter custom unit type"
                className="flex-1"
                onKeyPress={(e) => e.key === 'Enter' && addCustomType()}
              />
              <Button onClick={addCustomType} disabled={!customType.trim()}>
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {selectedTypes.length > 0 && (
        <Card className="p-6 bg-primary/5">
          <h3 className="font-semibold mb-4">Selected Unit Types</h3>
          <div className="space-y-2">
            {selectedTypes.map((type) => (
              <div key={type.id} className="flex items-center justify-between p-2 bg-background rounded border">
                <span className="text-sm font-medium">{type.name}</span>
                {!defaultTypes.includes(type.name) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCustomType(type.name)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-4 p-3 bg-muted/50 rounded">
            <p className="text-sm text-muted-foreground">
              <strong>Total unit types:</strong> {selectedTypes.length}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              You can assign these types to specific units during the detailed configuration.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};