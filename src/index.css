@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* One Society Property Management - Futuristic Minimal Design System */
/* Primary Brand Color: #E53935 (Red) */

@layer base {
  :root {
    /* Base colors - Clean whites and soft grays */
    --background: 0 0% 99%;
    --foreground: 220 13% 9%;

    /* Card system - Pure white with subtle shadows */
    --card: 0 0% 100%;
    --card-foreground: 220 13% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 9%;

    /* Primary brand color - One Society Red */
    --primary: 4 90% 58%; /* #E53935 */
    --primary-foreground: 0 0% 100%;
    --primary-hover: 4 90% 52%;
    --primary-light: 4 90% 95%;
    --primary-glow: 4 90% 68%;

    /* Secondary - Soft grays for minimal aesthetic */
    --secondary: 220 13% 96%;
    --secondary-foreground: 220 13% 9%;

    /* Muted colors - Subtle and clean */
    --muted: 220 13% 95%;
    --muted-foreground: 220 9% 46%;

    /* Accent - Subtle red tint for highlights */
    --accent: 4 30% 94%;
    --accent-foreground: 220 13% 9%;

    /* Status colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 76% 95%;

    --warning: 45 93% 58%;
    --warning-foreground: 220 13% 9%;
    --warning-light: 45 93% 95%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs - Minimal and clean */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 4 90% 58%;

    /* Wizard specific tokens - Red theme */
    --wizard-bg: linear-gradient(135deg, hsl(4 90% 58%), hsl(4 90% 68%));
    --wizard-bg-dark: linear-gradient(135deg, hsl(4 90% 45%), hsl(4 90% 55%));
    --wizard-step-active: 4 90% 58%;
    --wizard-step-complete: 142 76% 36%;
    --wizard-step-inactive: 220 13% 85%;
    --wizard-progress-bg: 220 13% 88%;

    /* Enhanced shadows for depth */
    --shadow-wizard: 0 20px 40px -12px hsl(4 90% 58% / 0.25);
    --shadow-card: 0 8px 32px -8px hsl(220 13% 9% / 0.08);
    --shadow-card-hover: 0 12px 40px -8px hsl(220 13% 9% / 0.12);
    --shadow-button: 0 4px 16px -4px hsl(4 90% 58% / 0.3);
    --shadow-input: 0 2px 8px -2px hsl(220 13% 9% / 0.05);

    /* Smooth transitions for premium feel */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Modern border radius */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;

    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 220 13% 9%;
    --sidebar-primary: 4 90% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 13% 96%;
    --sidebar-accent-foreground: 220 13% 9%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 4 90% 58%;
  }

  .dark {
    /* Dark mode with red accents */
    --background: 220 13% 6%;
    --foreground: 0 0% 98%;

    --card: 220 13% 8%;
    --card-foreground: 0 0% 98%;

    --popover: 220 13% 8%;
    --popover-foreground: 0 0% 98%;

    --primary: 4 90% 58%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 4 90% 52%;
    --primary-light: 4 90% 15%;

    --secondary: 220 13% 12%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 13% 12%;
    --muted-foreground: 220 9% 65%;

    --accent: 220 13% 12%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 15%;
    --input: 220 13% 15%;
    --ring: 4 90% 58%;

    /* Dark wizard theme */
    --wizard-bg: linear-gradient(135deg, hsl(220 13% 6%), hsl(220 13% 10%));
    --wizard-bg-dark: linear-gradient(135deg, hsl(220 13% 4%), hsl(220 13% 8%));

    /* Dark sidebar */
    --sidebar-background: 220 13% 6%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 4 90% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 13% 12%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 220 13% 15%;
    --sidebar-ring: 4 90% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  /* Enhanced typography scale */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl font-bold;
    letter-spacing: -0.02em;
  }

  h2 {
    @apply text-3xl md:text-4xl;
    letter-spacing: -0.015em;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
  }

  /* Smooth transitions for all interactive elements */
  button, input, select, textarea, [role="button"] {
    transition: var(--transition-smooth);
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

/* Component layer for reusable patterns */
@layer components {
  /* Glass morphism effect */
  .glass {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  .glass-dark {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(17, 25, 40, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced button styles */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary-hover;
    box-shadow: var(--shadow-button);
    transition: var(--transition-smooth);
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px -6px hsl(var(--primary) / 0.4);
  }

  /* Card enhancements */
  .card-enhanced {
    @apply bg-card border border-border rounded-lg;
    box-shadow: var(--shadow-card);
    transition: var(--transition-smooth);
  }

  .card-enhanced:hover {
    box-shadow: var(--shadow-card-hover);
    transform: translateY(-2px);
  }

  /* Input enhancements */
  .input-enhanced {
    @apply border-input bg-background;
    box-shadow: var(--shadow-input);
    transition: var(--transition-smooth);
  }

  .input-enhanced:focus {
    @apply border-primary;
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
  }
}