export type TenantType = 'individual' | 'commercial' | 'society';

export interface WizardStep {
  id: string;
  title: string;
  description?: string;
  component: string;
  isRequired: boolean;
}

export interface WizardFlow {
  tenantType: TenantType;
  steps: WizardStep[];
  currentStep: number;
  completedSteps: number[];
}

export interface AdminProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName?: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
}

export interface PropertyConfig {
  // Basic Infrastructure
  societyType?: 'residential' | 'commercial' | 'mixed';
  numberOfGates?: number;
  numberOfMainGates?: number;
  numberOfInnerGates?: number;
  numberOfBuildings?: number;
  buildingNamePattern?: string;
  buildingNamingPattern?: string; // Legacy compatibility
  customBuildingNames?: string[];
  
  // Buildings & Floors
  equalFloors?: boolean;
  floorsPerBuilding?: number | number[];
  floorNamingPattern?: string;
  
  // Units
  unitTypes?: UnitType[];
  equalUnits?: boolean;
  unitsPerFloor?: number | number[][];
  unitNamingPattern?: string;
  
  // Amenities & Parking
  amenities?: Amenity[];
  parkingTypes?: ParkingType[];
  
  // Legacy fields for compatibility
  propertyType?: string;
  parkingSpaces?: number;
}

export interface UnitType {
  id: string;
  name: string;
  category: 'residential' | 'commercial';
  description?: string;
  area?: number;
}

export interface Amenity {
  id: string;
  name: string;
  description?: string;
  isSelected?: boolean;
}

export interface ParkingType {
  id: string;
  type: string;
  count: number;
  description?: string;
}

export interface Member {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: 'primary' | 'secondary';
  unitId?: string;
  unitNumber?: string;
}

export interface OpeningBalance {
  unitId: string;
  unitNumber: string;
  balance: number;
  type: 'credit' | 'debit';
}

export interface BillingRule {
  id: string;
  name: string;
  type: string;
  frequency: 'monthly' | 'quarterly' | 'annually';
  amount: number;
  dueDate?: number;
  penalty?: number;
}

export interface TestBill {
  unitId: string;
  unitNumber: string;
  charges: BillingRule[];
  total: number;
  generatedAt: string;
}

export interface WizardData {
  tenantType?: TenantType;
  adminProfile?: AdminProfile;
  propertyConfig?: Partial<PropertyConfig>;
  memberAllotments?: Member[];
  openingBalances?: OpeningBalance[];
  billingRules?: BillingRule[];
  testBill?: TestBill;
}