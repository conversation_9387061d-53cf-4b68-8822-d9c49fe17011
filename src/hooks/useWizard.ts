import { useState, useCallback } from 'react';
import { TenantType, WizardFlow, WizardStep, WizardData } from '@/types/wizard';

const WIZARD_FLOWS: Record<TenantType, WizardStep[]> = {
  individual: [
    { 
      id: 'admin-profile', 
      title: 'Admin Profile', 
      description: 'Set up your administrative profile and contact information',
      component: 'AdminProfile', 
      isRequired: true 
    },
    { 
      id: 'property-basic', 
      title: 'Basic Infrastructure', 
      description: 'Define your property type and basic structure',
      component: 'PropertyBasic', 
      isRequired: true 
    },
    { 
      id: 'amenities', 
      title: 'Amenities & Parking', 
      description: 'Configure available amenities and parking facilities',
      component: 'Amenities', 
      isRequired: false 
    },
    { 
      id: 'opening-balances', 
      title: 'Opening Balances', 
      description: 'Set initial financial balances for accounts',
      component: 'OpeningBalances', 
      isRequired: false 
    },
    { 
      id: 'billing-rules', 
      title: 'Billing Configuration', 
      description: 'Define billing rules and payment schedules',
      component: 'BillingRules', 
      isRequired: true 
    },
    { 
      id: 'test-bill', 
      title: 'Test Run', 
      description: 'Generate and validate a test bill',
      component: 'TestBill', 
      isRequired: true 
    },
    { 
      id: 'review', 
      title: 'Review & Complete', 
      description: 'Review all settings and complete setup',
      component: 'Review', 
      isRequired: true 
    },
  ],
  commercial: [
    { 
      id: 'admin-profile', 
      title: 'Admin Profile', 
      description: 'Set up your administrative profile and contact information',
      component: 'AdminProfile', 
      isRequired: true 
    },
    { 
      id: 'property-basic', 
      title: 'Basic Infrastructure', 
      description: 'Define society type, gates, and building structure',
      component: 'PropertyBasic', 
      isRequired: true 
    },
    { 
      id: 'buildings-floors', 
      title: 'Buildings & Floors', 
      description: 'Configure building and floor layouts',
      component: 'BuildingsFloors', 
      isRequired: true 
    },
    { 
      id: 'floors-units', 
      title: 'Floors & Units', 
      description: 'Define unit distribution across floors',
      component: 'FloorsUnits', 
      isRequired: true 
    },
    { 
      id: 'unit-types', 
      title: 'Commercial Unit Types', 
      description: 'Define types of commercial units available',
      component: 'UnitTypes', 
      isRequired: true 
    },
    { 
      id: 'naming-patterns', 
      title: 'Naming Patterns', 
      description: 'Set up naming conventions for buildings, floors, and units',
      component: 'NamingPatterns', 
      isRequired: true 
    },
    { 
      id: 'parking-amenities', 
      title: 'Amenities & Parking', 
      description: 'Configure parking and commercial amenities',
      component: 'ParkingAmenities', 
      isRequired: false 
    },
    { 
      id: 'member-allotment', 
      title: 'Member Allotment', 
      description: 'Assign tenants to commercial units',
      component: 'MemberAllotment', 
      isRequired: false 
    },
    { 
      id: 'opening-balances', 
      title: 'Opening Balances', 
      description: 'Set initial account balances for all units',
      component: 'OpeningBalances', 
      isRequired: false 
    },
    { 
      id: 'billing-rules', 
      title: 'Billing Configuration', 
      description: 'Configure commercial billing rules and schedules',
      component: 'BillingRules', 
      isRequired: true 
    },
    { 
      id: 'test-bill', 
      title: 'Test Run', 
      description: 'Generate and validate test bills',
      component: 'TestBill', 
      isRequired: true 
    },
    { 
      id: 'review', 
      title: 'Review & Complete', 
      description: 'Final review and system activation',
      component: 'Review', 
      isRequired: true 
    },
  ],
  society: [
    { 
      id: 'admin-profile', 
      title: 'Admin Profile', 
      description: 'Set up your administrative profile and contact information',
      component: 'AdminProfile', 
      isRequired: true 
    },
    { 
      id: 'property-basic', 
      title: 'Basic Infrastructure', 
      description: 'Define society type, gates, and building structure',
      component: 'PropertyBasic', 
      isRequired: true 
    },
    { 
      id: 'buildings-floors', 
      title: 'Buildings & Floors', 
      description: 'Configure building and floor layouts',
      component: 'BuildingsFloors', 
      isRequired: true 
    },
    { 
      id: 'floors-units', 
      title: 'Floors & Units', 
      description: 'Define unit distribution across floors',
      component: 'FloorsUnits', 
      isRequired: true 
    },
    { 
      id: 'unit-types', 
      title: 'Unit Types', 
      description: 'Define residential unit types (2BHK, 3BHK, etc.)',
      component: 'UnitTypes', 
      isRequired: true 
    },
    { 
      id: 'naming-patterns', 
      title: 'Naming Patterns', 
      description: 'Set up naming conventions for buildings, floors, and units',
      component: 'NamingPatterns', 
      isRequired: true 
    },
    { 
      id: 'parking-amenities', 
      title: 'Amenities & Parking', 
      description: 'Configure society amenities and parking facilities',
      component: 'ParkingAmenities', 
      isRequired: false 
    },
    { 
      id: 'member-allotment', 
      title: 'Member Allotment', 
      description: 'Assign residents to units and manage family members',
      component: 'MemberAllotment', 
      isRequired: false 
    },
    { 
      id: 'opening-balances', 
      title: 'Opening Balances', 
      description: 'Set initial account balances for all unit ledgers',
      component: 'OpeningBalances', 
      isRequired: false 
    },
    { 
      id: 'billing-rules', 
      title: 'Billing Configuration', 
      description: 'Configure CAM charges, sinking fund, and other billing rules',
      component: 'BillingRules', 
      isRequired: true 
    },
    { 
      id: 'test-bill', 
      title: 'Test Run', 
      description: 'Generate and validate test bills for sample units',
      component: 'TestBill', 
      isRequired: true 
    },
    { 
      id: 'review', 
      title: 'Review & Complete', 
      description: 'Final review and dashboard activation',
      component: 'Review', 
      isRequired: true 
    },
  ],
};

export const useWizard = () => {
  const [wizardFlow, setWizardFlow] = useState<WizardFlow | null>(null);
  const [wizardData, setWizardData] = useState<WizardData>({});

  const initializeWizard = useCallback((tenantType: TenantType) => {
    const steps = WIZARD_FLOWS[tenantType];
    setWizardFlow({
      tenantType,
      steps,
      currentStep: 0,
      completedSteps: [],
    });
    setWizardData({ tenantType });
  }, []);

  const nextStep = useCallback(() => {
    if (!wizardFlow) return;
    
    const nextStepIndex = wizardFlow.currentStep + 1;
    if (nextStepIndex < wizardFlow.steps.length) {
      setWizardFlow(prev => prev ? {
        ...prev,
        currentStep: nextStepIndex,
        completedSteps: [...prev.completedSteps, prev.currentStep],
      } : null);
    }
  }, [wizardFlow]);

  const previousStep = useCallback(() => {
    if (!wizardFlow) return;
    
    const prevStepIndex = wizardFlow.currentStep - 1;
    if (prevStepIndex >= 0) {
      setWizardFlow(prev => prev ? {
        ...prev,
        currentStep: prevStepIndex,
      } : null);
    }
  }, [wizardFlow]);

  const goToStep = useCallback((stepIndex: number) => {
    if (!wizardFlow || stepIndex < 0 || stepIndex >= wizardFlow.steps.length) return;
    
    setWizardFlow(prev => prev ? {
      ...prev,
      currentStep: stepIndex,
    } : null);
  }, [wizardFlow]);

  const updateWizardData = useCallback((data: Partial<WizardData>) => {
    setWizardData(prev => ({ ...prev, ...data }));
  }, []);

  const getProgressPercentage = useCallback(() => {
    if (!wizardFlow) return 0;
    return Math.round(((wizardFlow.currentStep + 1) / wizardFlow.steps.length) * 100);
  }, [wizardFlow]);

  const isStepCompleted = useCallback((stepIndex: number) => {
    if (!wizardFlow) return false;
    return wizardFlow.completedSteps.includes(stepIndex);
  }, [wizardFlow]);

  const canProceed = useCallback(() => {
    if (!wizardFlow) return false;
    
    const currentStepData = wizardFlow.steps[wizardFlow.currentStep];
    
    // For the final review step, require all previous required steps to be completed
    if (currentStepData.id === 'review') {
      return Boolean(wizardData.adminProfile?.firstName && wizardData.adminProfile?.email &&
                    wizardData.propertyConfig?.propertyType && wizardData.propertyConfig?.numberOfBuildings);
    }
    
    // For all other steps, allow navigation but encourage completion
    return true;
  }, [wizardFlow, wizardData]);

  return {
    wizardFlow,
    wizardData,
    initializeWizard,
    nextStep,
    previousStep,
    goToStep,
    updateWizardData,
    getProgressPercentage,
    isStepCompleted,
    canProceed,
  };
};